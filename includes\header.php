<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo isset($pageTitle) ? $pageTitle . ' - ' . SITE_NAME : SITE_NAME . ' - ' . SITE_DESCRIPTION; ?></title>
    <meta name="description" content="<?php echo isset($pageDescription) ? $pageDescription : SITE_DESCRIPTION; ?>">
    
    <!-- Tailwind CSS con configuración personalizada -->
    <script src="https://cdn.tailwindcss.com"></script>
    <script>
        tailwind.config = {
            theme: {
                extend: {
                    colors: {
                        'primary': '#1D5D20',
                        'secondary': '#437845',
                        'accent': '#68936A',
                        'light-green': '#8EAE90',
                        'lighter-green': '#B4C9B5',
                        'lightest-green': '#D9E4DA'
                    },
                    screens: {
                        'compact': '480px',    // Foldables (outer), Large phones
                        'tablet': '768px',     // Tablets, Foldables (inner)  
                        'laptop': '1024px',    // 13-14" Laptops
                        'desktop': '1280px',   // Standard Desktops
                        'qhd': '1600px',       // QHD/2K
                        'uhd': '2560px'        // 4K, Ultra-wide
                    }
                }
            }
        }
    </script>
    
    <!-- FontAwesome -->
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.0/css/all.min.css" rel="stylesheet">
    
    <!-- Estilos personalizados -->
    <style>
        .sidebar-curve {
            clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 0 100%);
        }
        
        .nav-item:hover {
            transform: translateX(5px);
            transition: transform 0.3s ease;
        }
        
        .dropdown-enter {
            opacity: 0;
            transform: translateY(-10px);
        }
        
        .dropdown-enter-active {
            opacity: 1;
            transform: translateY(0);
            transition: all 0.3s ease;
        }
        
        .animate-fade-in {
            animation: fadeIn 0.8s ease-in-out;
        }
        
        @keyframes fadeIn {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        /* Responsive breakpoints personalizados */
        @media (min-width: 480px) { .compact\:block { display: block; } }
        @media (min-width: 768px) { .tablet\:flex { display: flex; } }
        @media (min-width: 1024px) { .laptop\:grid { display: grid; } }
        @media (min-width: 1280px) { .desktop\:max-w-7xl { max-width: 80rem; } }
        @media (min-width: 1600px) { .qhd\:max-w-8xl { max-width: 96rem; } }
        @media (min-width: 2560px) { .uhd\:max-w-full { max-width: 100%; } }
    </style>
</head>
<body class="bg-lightest-green min-h-screen">
    
    <!-- Sidebar -->
    <div id="sidebar" class="fixed left-0 top-0 h-full w-80 bg-gradient-to-b from-primary to-secondary sidebar-curve transform -translate-x-full transition-transform duration-300 ease-in-out z-50 shadow-2xl">
        <!-- Logo Section -->
        <div class="p-6 border-b border-accent/30">
            <div class="flex items-center space-x-3">
                <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                    <i class="fas fa-paw text-primary text-xl"></i>
                </div>
                <h1 class="text-white text-xl font-bold"><?php echo SITE_NAME; ?></h1>
            </div>
        </div>

        <!-- Navigation Links -->
        <nav class="p-4 space-y-2 overflow-y-auto max-h-[calc(100vh-200px)]">
            <?php foreach (NAVIGATION_ITEMS as $key => $item): ?>
            <div class="nav-item">
                <button class="nav-button w-full flex items-center justify-between p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200 <?php echo isActivePage($key) ? 'bg-accent/30' : ''; ?>" 
                        data-dropdown="<?php echo $key; ?>">
                    <div class="flex items-center space-x-3">
                        <i class="<?php echo $item['icon']; ?> text-lg"></i>
                        <span><?php echo $item['title']; ?></span>
                    </div>
                    <?php if (!empty($item['dropdown'])): ?>
                    <i class="fas fa-chevron-down transition-transform duration-200"></i>
                    <?php endif; ?>
                </button>
                
                <?php if (!empty($item['dropdown'])): ?>
                <div id="dropdown-<?php echo $key; ?>" class="dropdown hidden ml-6 mt-2 space-y-1">
                    <?php foreach ($item['dropdown'] as $dropKey => $dropItem): ?>
                    <a href="<?php echo $dropItem['url']; ?>" class="block p-2 text-lighter-green hover:text-white transition-colors">
                        <i class="fas fa-video mr-2"></i><?php echo $dropItem['title']; ?>
                    </a>
                    <?php endforeach; ?>
                </div>
                <?php endif; ?>
            </div>
            <?php endforeach; ?>
        </nav>

        <!-- Bottom Icons -->
        <div class="absolute bottom-6 left-6 right-6 border-t border-accent/30 pt-4">
            <div class="flex justify-center space-x-4">
                <!-- Carrito -->
                <button class="relative p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" onclick="toggleCart()">
                    <i class="fas fa-shopping-cart text-lg"></i>
                    <?php if (getCartCount() > 0): ?>
                    <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        <?php echo getCartCount(); ?>
                    </span>
                    <?php endif; ?>
                </button>
                
                <!-- Perfil -->
                <button class="p-3 text-white hover:bg-accent/20 rounded-lg transition-all duration-200" onclick="toggleProfile()">
                    <i class="fas fa-user text-lg"></i>
                </button>
            </div>
        </div>
    </div>

    <!-- Main Content -->
    <div id="main-content" class="transition-all duration-300 ease-in-out">
        <!-- Header -->
        <header class="bg-white shadow-lg sticky top-0 z-40">
            <div class="container mx-auto px-4 py-4 desktop:max-w-7xl qhd:max-w-8xl uhd:max-w-full">
                <div class="flex items-center justify-between">
                    <!-- Left Side - Menu Button and Logo -->
                    <div class="flex items-center space-x-4">
                        <button id="menu-toggle" class="p-2 text-primary hover:bg-lightest-green rounded-lg transition-colors">
                            <i class="fas fa-bars text-xl"></i>
                        </button>
                        <a href="index.php" class="flex items-center space-x-3">
                            <div class="w-10 h-10 bg-primary rounded-full flex items-center justify-center">
                                <i class="fas fa-paw text-white"></i>
                            </div>
                            <h1 class="text-primary text-2xl font-bold hidden compact:block"><?php echo SITE_NAME; ?></h1>
                        </a>
                    </div>

                    <!-- Center Navigation (Desktop) -->
                    <nav class="hidden laptop:flex space-x-6">
                        <?php foreach (array_slice(NAVIGATION_ITEMS, 0, 4) as $key => $item): ?>
                        <a href="<?php echo $item['url']; ?>" 
                           class="text-primary hover:text-secondary font-medium transition-colors <?php echo isActivePage($key) ? 'text-secondary border-b-2 border-secondary' : ''; ?>">
                            <?php echo $item['title']; ?>
                        </a>
                        <?php endforeach; ?>
                    </nav>

                    <!-- Right Side Icons -->
                    <div class="flex items-center space-x-4">
                        <!-- Search (Desktop) -->
                        <div class="hidden tablet:block">
                            <div class="relative">
                                <input type="text" placeholder="Buscar..." 
                                       class="pl-10 pr-4 py-2 border border-lighter-green rounded-lg focus:outline-none focus:ring-2 focus:ring-primary">
                                <i class="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"></i>
                            </div>
                        </div>
                        
                        <!-- Carrito -->
                        <button class="relative p-2 text-primary hover:bg-lightest-green rounded-lg transition-colors" onclick="toggleCart()">
                            <i class="fas fa-shopping-cart text-xl"></i>
                            <?php if (getCartCount() > 0): ?>
                            <span class="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                                <?php echo getCartCount(); ?>
                            </span>
                            <?php endif; ?>
                        </button>
                        
                        <!-- Perfil -->
                        <div class="relative">
                            <button class="p-2 text-primary hover:bg-lightest-green rounded-lg transition-colors" onclick="toggleProfile()">
                                <i class="fas fa-user text-xl"></i>
                            </button>
                            
                            <!-- Dropdown de perfil -->
                            <div id="profile-dropdown" class="hidden absolute right-0 mt-2 w-48 bg-white rounded-lg shadow-lg border border-lighter-green">
                                <?php if (isLoggedIn()): ?>
                                    <?php $user = getCurrentUser(); ?>
                                    <div class="p-4 border-b border-lighter-green">
                                        <p class="font-semibold text-primary"><?php echo htmlspecialchars($user['name']); ?></p>
                                        <p class="text-sm text-gray-600"><?php echo htmlspecialchars($user['email']); ?></p>
                                    </div>
                                    <a href="profile.php" class="block px-4 py-2 text-gray-700 hover:bg-lightest-green">
                                        <i class="fas fa-user mr-2"></i>Mi Perfil
                                    </a>
                                    <a href="orders.php" class="block px-4 py-2 text-gray-700 hover:bg-lightest-green">
                                        <i class="fas fa-shopping-bag mr-2"></i>Mis Pedidos
                                    </a>
                                    <a href="logout.php" class="block px-4 py-2 text-gray-700 hover:bg-lightest-green">
                                        <i class="fas fa-sign-out-alt mr-2"></i>Cerrar Sesión
                                    </a>
                                <?php else: ?>
                                    <a href="login.php" class="block px-4 py-2 text-gray-700 hover:bg-lightest-green">
                                        <i class="fas fa-sign-in-alt mr-2"></i>Iniciar Sesión
                                    </a>
                                    <a href="register.php" class="block px-4 py-2 text-gray-700 hover:bg-lightest-green">
                                        <i class="fas fa-user-plus mr-2"></i>Registrarse
                                    </a>
                                <?php endif; ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </header>
