# AnimPlanetas - Sistema de Pestañas con Sidebar

## Descripción

AnimPlanetas es una plataforma educativa elegante y moderna para el aprendizaje del lenguaje de señas, enfocada en la conexión con el mundo animal. El sistema incluye un sidebar interactivo con navegación por pestañas, diseño responsivo y una experiencia de usuario optimizada.

## Características Principales

### 🎨 Diseño Visual
- **Paleta de colores temática**: 
  - Primary: #1D5D20 (Verde oscuro)
  - Secondary: #437845 (Verde medio)
  - Accent: #68936A (Verde claro)
  - Light Green: #8EAE90
  - Lighter Green: #B4C9B5
  - Lightest Green: #D9E4DA
  - White: #FFFFFF

### 🚀 Funcionalidades

#### Sidebar Interactivo
- **Diseño curvo elegante** con clip-path personalizado
- **Navegación por pestañas** con dropdowns animados
- **Iconografía FontAwesome** para cada sección
- **Animaciones suaves** de entrada y salida
- **Overlay semi-transparente** para móviles

#### Secciones de Navegación
1. **Inicio** - Página principal con video de señas
2. **AnimaleSeñas** - Señas de animales con videos
3. **PlanetaSeñas** - Señas planetarias con videos
4. **DiccionarioSeñas** - Diccionario completo con videos
5. **JuegoSeñas** - Juegos interactivos con videos
6. **VideoSeñas** - Biblioteca de videos
7. **ReflexiónSeñas** - Contenido reflexivo con videos
8. **TiendaSeñas** - Tienda con carrito de compras

#### Componentes de la Página
- **Header responsivo** con logo y controles
- **Hero section** con carousel automático
- **CTA (Call to Action)** con botones interactivos
- **Sección de estadísticas** con contadores animados
- **Blog/Contenido** con tarjetas elegantes
- **Equipo** con perfiles del equipo
- **Testimonios** con calificaciones por estrellas
- **Galería** con carousel de imágenes
- **Footer** completo con enlaces y contacto

### 📱 Diseño Responsivo (2025)

| Breakpoint | Dispositivos Objetivo | Rango de Resolución | Clase Tailwind |
|------------|----------------------|-------------------|----------------|
| Mobile | Smartphones | 320px - 768px | `sm:` |
| Tablet | Tablets | 768px - 1024px | `md:` |
| Desktop | Computadoras | 1024px - 1440px | `lg:` |
| Large Desktop | Pantallas grandes | 1440px+ | `xl:` |

### 🎯 Características de Accesibilidad
- **Navegación por teclado** completa
- **ARIA labels** y roles semánticos
- **Contraste alto** para legibilidad
- **Soporte para lectores de pantalla**
- **Reducción de movimiento** para usuarios sensibles
- **Modo oscuro** automático

### ⚡ Optimizaciones de Rendimiento
- **Lazy loading** para imágenes
- **Animaciones CSS optimizadas**
- **JavaScript modular** y eficiente
- **Compresión de assets**
- **Cache de recursos**

## Estructura de Archivos

```
views/frontend/Sistema-Pestañas/
├── indexMainPage.html      # Página principal
├── styles.css             # Estilos personalizados
├── script.js              # JavaScript funcional
└── README.md              # Documentación
```

## Instalación y Uso

### Requisitos
- Navegador web moderno (Chrome 90+, Firefox 88+, Safari 14+)
- Conexión a internet para CDNs (Tailwind CSS, FontAwesome)

### Configuración
1. Clona o descarga los archivos del proyecto
2. Asegúrate de que todos los archivos estén en la misma carpeta
3. Abre `indexMainPage.html` en tu navegador

### Personalización

#### Colores
Modifica las variables CSS en `styles.css`:
```css
:root {
    --primary: #1D5D20;
    --secondary: #437845;
    /* ... más colores */
}
```

#### Contenido
- Edita el HTML para cambiar textos e imágenes
- Modifica los enlaces de navegación según tus necesidades
- Actualiza la información de contacto en el footer

#### Funcionalidades
- Ajusta los tiempos de animación en `script.js`
- Modifica el comportamiento del carousel
- Personaliza las interacciones del sidebar

## Tecnologías Utilizadas

- **HTML5** - Estructura semántica
- **Tailwind CSS** - Framework de utilidades CSS
- **JavaScript ES6+** - Interactividad moderna
- **FontAwesome 6** - Iconografía
- **CSS Grid & Flexbox** - Layout responsivo
- **CSS Animations** - Transiciones suaves

## Compatibilidad de Navegadores

| Navegador | Versión Mínima | Soporte |
|-----------|----------------|---------|
| Chrome | 90+ | ✅ Completo |
| Firefox | 88+ | ✅ Completo |
| Safari | 14+ | ✅ Completo |
| Edge | 90+ | ✅ Completo |
| Opera | 76+ | ✅ Completo |

## Funcionalidades JavaScript

### Clase Principal: `AnimPlanetasApp`
- **Gestión del sidebar** con toggle y overlay
- **Control de dropdowns** con animaciones
- **Carousel automático** con controles manuales
- **Navegación por teclado** (Escape, flechas)
- **Efectos de scroll** y progress indicator
- **Animaciones de entrada** con Intersection Observer
- **Contadores animados** para estadísticas

### Eventos Principales
- `click` - Interacciones de botones y navegación
- `keydown` - Navegación por teclado
- `scroll` - Efectos de parallax y progress
- `resize` - Adaptación responsiva
- `mouseenter/mouseleave` - Hover effects

## Optimización SEO

- **Meta tags** apropiados
- **Estructura semántica** HTML5
- **Alt text** para imágenes
- **Títulos jerárquicos** (H1-H6)
- **URLs amigables** preparadas
- **Schema markup** listo para implementar

## Próximas Mejoras

- [ ] Integración con backend para contenido dinámico
- [ ] Sistema de autenticación de usuarios
- [ ] Carrito de compras funcional
- [ ] Reproductor de video integrado
- [ ] Sistema de comentarios y calificaciones
- [ ] Modo offline con Service Workers
- [ ] Análiticas y tracking de usuarios

## Soporte y Contacto

Para soporte técnico o consultas sobre el proyecto:
- Email: <EMAIL>
- Documentación: [Enlace a docs]
- Issues: [Enlace a repositorio]

---

**AnimPlanetas** - Conectando el mundo animal con el lenguaje de señas 🐾
