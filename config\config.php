<?php
/**
 * AnimPlanetas - Configuración Principal
 * Sistema de Pestañas con PHP
 */

// Configuración de la base de datos
define('DB_HOST', 'localhost');
define('DB_NAME', 'animplanetas');
define('DB_USER', 'root');
define('DB_PASS', '');

// Configuración del sitio
define('SITE_NAME', 'AnimPlanetas');
define('SITE_DESCRIPTION', 'Conectando el mundo animal con el lenguaje de señas');
define('SITE_URL', 'http://localhost/animplanetas');
define('SITE_VERSION', '1.0.0');

// Rutas del sistema
define('ROOT_PATH', dirname(__DIR__));
define('INCLUDES_PATH', ROOT_PATH . '/includes');
define('COMPONENTS_PATH', ROOT_PATH . '/components');
define('ASSETS_PATH', ROOT_PATH . '/assets');
define('UPLOADS_PATH', ROOT_PATH . '/uploads');

// Configuración de sesiones
ini_set('session.cookie_httponly', 1);
ini_set('session.use_only_cookies', 1);
ini_set('session.cookie_secure', 0); // Cambiar a 1 en HTTPS
session_start();

// Paleta de colores del sistema
define('COLORS', [
    'primary' => '#1D5D20',
    'secondary' => '#437845', 
    'accent' => '#68936A',
    'light_green' => '#8EAE90',
    'lighter_green' => '#B4C9B5',
    'lightest_green' => '#D9E4DA',
    'white' => '#FFFFFF'
]);

// Configuración de breakpoints 2025
define('BREAKPOINTS', [
    'compact' => '480px',    // Foldables (outer), Large phones
    'tablet' => '768px',     // Tablets, Foldables (inner)
    'laptop' => '1024px',    // 13-14" Laptops
    'desktop' => '1280px',   // Standard Desktops
    'qhd' => '1600px',       // QHD/2K
    'uhd' => '2560px'        // 4K, Ultra-wide
]);

// Configuración de navegación
define('NAVIGATION_ITEMS', [
    'inicio' => [
        'title' => 'Inicio',
        'icon' => 'fas fa-home',
        'url' => 'index.php',
        'dropdown' => [
            'video_inicio' => ['title' => 'Video de Señas - Inicio', 'url' => 'videos.php?section=inicio']
        ]
    ],
    'animales' => [
        'title' => 'AnimaleSeñas',
        'icon' => 'fas fa-dog',
        'url' => 'animales.php',
        'dropdown' => [
            'video_animales' => ['title' => 'Video AnimaleSeñas', 'url' => 'videos.php?section=animales']
        ]
    ],
    'planeta' => [
        'title' => 'PlanetaSeñas',
        'icon' => 'fas fa-globe',
        'url' => 'planeta.php',
        'dropdown' => [
            'video_planeta' => ['title' => 'Video PlanetaSeñas', 'url' => 'videos.php?section=planeta']
        ]
    ],
    'diccionario' => [
        'title' => 'DiccionarioSeñas',
        'icon' => 'fas fa-book',
        'url' => 'diccionario.php',
        'dropdown' => [
            'video_diccionario' => ['title' => 'Video DiccionarioSeñas', 'url' => 'videos.php?section=diccionario']
        ]
    ],
    'juegos' => [
        'title' => 'JuegoSeñas',
        'icon' => 'fas fa-gamepad',
        'url' => 'juegos.php',
        'dropdown' => [
            'video_juegos' => ['title' => 'Video JuegoSeñas', 'url' => 'videos.php?section=juegos']
        ]
    ],
    'videos' => [
        'title' => 'VideoSeñas',
        'icon' => 'fas fa-play-circle',
        'url' => 'videos.php',
        'dropdown' => [
            'video_biblioteca' => ['title' => 'Video VideoSeñas', 'url' => 'videos.php?section=biblioteca']
        ]
    ],
    'reflexion' => [
        'title' => 'ReflexiónSeñas',
        'icon' => 'fas fa-lightbulb',
        'url' => 'reflexion.php',
        'dropdown' => [
            'video_reflexion' => ['title' => 'Video ReflexiónSeñas', 'url' => 'videos.php?section=reflexion']
        ]
    ],
    'tienda' => [
        'title' => 'TiendaSeñas',
        'icon' => 'fas fa-shopping-cart',
        'url' => 'tienda.php',
        'dropdown' => [
            'video_tienda' => ['title' => 'Video TiendaSeñas', 'url' => 'videos.php?section=tienda'],
            'carrito' => ['title' => 'Carrito', 'url' => 'carrito.php']
        ]
    ]
]);

// Configuración de usuario
define('USER_ROLES', [
    'guest' => 0,
    'user' => 1,
    'admin' => 2
]);

// Funciones de utilidad
function getCurrentPage() {
    return basename($_SERVER['PHP_SELF'], '.php');
}

function isActivePage($page) {
    return getCurrentPage() === $page;
}

function getAssetUrl($path) {
    return SITE_URL . '/assets/' . $path;
}

function includeComponent($component) {
    $file = COMPONENTS_PATH . '/' . $component . '.php';
    if (file_exists($file)) {
        include $file;
    }
}

function sanitizeInput($input) {
    return htmlspecialchars(strip_tags(trim($input)), ENT_QUOTES, 'UTF-8');
}

function redirectTo($url) {
    header("Location: $url");
    exit();
}

// Configuración de errores (desarrollo)
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Configuración de zona horaria
date_default_timezone_set('America/Mexico_City');

// Autoload de clases (si se usan)
spl_autoload_register(function ($class) {
    $file = ROOT_PATH . '/classes/' . $class . '.php';
    if (file_exists($file)) {
        require_once $file;
    }
});
?>
