<?php
/**
 * AnimPlanetas - Conexión a Base de Datos
 */

class Database {
    private $host;
    private $dbname;
    private $username;
    private $password;
    private $pdo;
    
    public function __construct() {
        $this->host = DB_HOST;
        $this->dbname = DB_NAME;
        $this->username = DB_USER;
        $this->password = DB_PASS;
    }
    
    public function connect() {
        if ($this->pdo === null) {
            try {
                $dsn = "mysql:host={$this->host};dbname={$this->dbname};charset=utf8mb4";
                $options = [
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::ATTR_EMULATE_PREPARES => false,
                ];
                
                $this->pdo = new PDO($dsn, $this->username, $this->password, $options);
            } catch (PDOException $e) {
                throw new Exception("Error de conexión: " . $e->getMessage());
            }
        }
        
        return $this->pdo;
    }
    
    public function query($sql, $params = []) {
        try {
            $stmt = $this->connect()->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (PDOException $e) {
            throw new Exception("Error en consulta: " . $e->getMessage());
        }
    }
    
    public function fetch($sql, $params = []) {
        return $this->query($sql, $params)->fetch();
    }
    
    public function fetchAll($sql, $params = []) {
        return $this->query($sql, $params)->fetchAll();
    }
    
    public function insert($table, $data) {
        $columns = implode(',', array_keys($data));
        $placeholders = ':' . implode(', :', array_keys($data));
        
        $sql = "INSERT INTO {$table} ({$columns}) VALUES ({$placeholders})";
        $this->query($sql, $data);
        
        return $this->connect()->lastInsertId();
    }
    
    public function update($table, $data, $where, $whereParams = []) {
        $set = [];
        foreach ($data as $key => $value) {
            $set[] = "{$key} = :{$key}";
        }
        $setClause = implode(', ', $set);
        
        $sql = "UPDATE {$table} SET {$setClause} WHERE {$where}";
        $params = array_merge($data, $whereParams);
        
        return $this->query($sql, $params);
    }
    
    public function delete($table, $where, $params = []) {
        $sql = "DELETE FROM {$table} WHERE {$where}";
        return $this->query($sql, $params);
    }
}

// Instancia global de la base de datos
$db = new Database();

// Funciones de utilidad para la base de datos
function getVideos($section = null) {
    global $db;
    
    $sql = "SELECT * FROM videos WHERE status = 'active'";
    $params = [];
    
    if ($section) {
        $sql .= " AND section = :section";
        $params['section'] = $section;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    return $db->fetchAll($sql, $params);
}

function getBlogPosts($limit = 10) {
    global $db;
    
    $sql = "SELECT * FROM blog_posts WHERE status = 'published' ORDER BY created_at DESC LIMIT :limit";
    return $db->fetchAll($sql, ['limit' => $limit]);
}

function getProducts($category = null, $limit = null) {
    global $db;
    
    $sql = "SELECT * FROM products WHERE status = 'active'";
    $params = [];
    
    if ($category) {
        $sql .= " AND category = :category";
        $params['category'] = $category;
    }
    
    $sql .= " ORDER BY created_at DESC";
    
    if ($limit) {
        $sql .= " LIMIT :limit";
        $params['limit'] = $limit;
    }
    
    return $db->fetchAll($sql, $params);
}

function getTestimonials($limit = 6) {
    global $db;
    
    $sql = "SELECT * FROM testimonials WHERE status = 'active' ORDER BY created_at DESC LIMIT :limit";
    return $db->fetchAll($sql, ['limit' => $limit]);
}

function getTeamMembers() {
    global $db;
    
    $sql = "SELECT * FROM team_members WHERE status = 'active' ORDER BY order_position ASC";
    return $db->fetchAll($sql);
}

function getStatistics() {
    global $db;
    
    $stats = [];
    
    // Usuarios activos
    $stats['users'] = $db->fetch("SELECT COUNT(*) as count FROM users WHERE status = 'active'")['count'] ?? 0;
    
    // Videos disponibles
    $stats['videos'] = $db->fetch("SELECT COUNT(*) as count FROM videos WHERE status = 'active'")['count'] ?? 0;
    
    // Productos en tienda
    $stats['products'] = $db->fetch("SELECT COUNT(*) as count FROM products WHERE status = 'active'")['count'] ?? 0;
    
    // Calificación promedio
    $stats['rating'] = $db->fetch("SELECT AVG(rating) as avg FROM testimonials WHERE status = 'active'")['avg'] ?? 4.9;
    
    return $stats;
}

function getCarouselItems() {
    global $db;
    
    $sql = "SELECT * FROM carousel_items WHERE status = 'active' ORDER BY order_position ASC";
    return $db->fetchAll($sql);
}

function getGalleryImages($limit = 12) {
    global $db;
    
    $sql = "SELECT * FROM gallery WHERE status = 'active' ORDER BY created_at DESC LIMIT :limit";
    return $db->fetchAll($sql, ['limit' => $limit]);
}

// Funciones de sesión y usuario
function isLoggedIn() {
    return isset($_SESSION['user_id']);
}

function getCurrentUser() {
    if (!isLoggedIn()) {
        return null;
    }
    
    global $db;
    return $db->fetch("SELECT * FROM users WHERE id = :id", ['id' => $_SESSION['user_id']]);
}

function getUserRole() {
    $user = getCurrentUser();
    return $user ? $user['role'] : 'guest';
}

function hasPermission($requiredRole) {
    $userRole = getUserRole();
    $roles = USER_ROLES;
    
    return $roles[$userRole] >= $roles[$requiredRole];
}

// Funciones de carrito
function getCartItems() {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    return $_SESSION['cart'];
}

function addToCart($productId, $quantity = 1) {
    if (!isset($_SESSION['cart'])) {
        $_SESSION['cart'] = [];
    }
    
    if (isset($_SESSION['cart'][$productId])) {
        $_SESSION['cart'][$productId] += $quantity;
    } else {
        $_SESSION['cart'][$productId] = $quantity;
    }
}

function removeFromCart($productId) {
    if (isset($_SESSION['cart'][$productId])) {
        unset($_SESSION['cart'][$productId]);
    }
}

function getCartTotal() {
    global $db;
    $cart = getCartItems();
    $total = 0;
    
    foreach ($cart as $productId => $quantity) {
        $product = $db->fetch("SELECT price FROM products WHERE id = :id", ['id' => $productId]);
        if ($product) {
            $total += $product['price'] * $quantity;
        }
    }
    
    return $total;
}

function getCartCount() {
    $cart = getCartItems();
    return array_sum($cart);
}
?>
