/* AnimPlanetas Custom Styles */

/* Color Variables */
:root {
    --primary: #1D5D20;
    --secondary: #437845;
    --accent: #68936A;
    --light-green: #8EAE90;
    --lighter-green: #B4C9B5;
    --lightest-green: #D9E4DA;
}

/* Responsive Breakpoints for 2025 */
/* Mobile: 320px - 768px */
/* Tablet: 768px - 1024px */
/* Desktop: 1024px - 1440px */
/* Large Desktop: 1440px+ */

/* Custom Animations */
@keyframes slideInFromLeft {
    from {
        transform: translateX(-100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideInFromRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        transform: translateY(50px);
        opacity: 0;
    }
    to {
        transform: translateY(0);
        opacity: 1;
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

/* Sidebar Enhancements */
.sidebar-curve {
    clip-path: polygon(0 0, calc(100% - 20px) 0, 100% 20px, 100% 100%, 0 100%);
    box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

.nav-item {
    position: relative;
    overflow: hidden;
}

.nav-item::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    transition: left 0.5s;
}

.nav-item:hover::before {
    left: 100%;
}

.nav-button {
    position: relative;
    z-index: 1;
}

/* Dropdown Animations */
.dropdown {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease-in-out;
}

.dropdown:not(.hidden) {
    max-height: 200px;
}

/* Hero Section Enhancements */
.hero-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 50%, var(--accent) 100%);
}

/* Card Hover Effects */
.card-hover {
    transition: all 0.3s ease;
}

.card-hover:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
}

/* Button Enhancements */
.btn-primary {
    background: linear-gradient(135deg, var(--primary), var(--secondary));
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.btn-primary::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.btn-primary:hover::before {
    left: 100%;
}

/* Carousel Enhancements */
.carousel-slide {
    transition: transform 0.5s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Statistics Counter Animation */
.stat-number {
    animation: fadeInUp 1s ease-out;
}

/* Testimonial Cards */
.testimonial-card {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(255, 255, 255, 0.7));
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Footer Enhancements */
.footer-gradient {
    background: linear-gradient(135deg, var(--primary) 0%, var(--secondary) 100%);
}

/* Responsive Design */
@media (max-width: 768px) {
    .sidebar-curve {
        clip-path: none;
        width: 100% !important;
    }
    
    #main-content.ml-80 {
        margin-left: 0 !important;
    }
    
    .hero-text {
        font-size: 2.5rem;
    }
    
    .carousel-controls {
        display: none;
    }
    
    .grid-responsive {
        grid-template-columns: 1fr;
    }
}

@media (min-width: 768px) and (max-width: 1024px) {
    .sidebar-curve {
        width: 280px;
    }
    
    .hero-text {
        font-size: 3.5rem;
    }
    
    .grid-responsive {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (min-width: 1024px) {
    .sidebar-curve {
        width: 320px;
    }
    
    .hero-text {
        font-size: 4rem;
    }
    
    .grid-responsive {
        grid-template-columns: repeat(3, 1fr);
    }
}

@media (min-width: 1440px) {
    .container {
        max-width: 1400px;
    }
    
    .hero-text {
        font-size: 5rem;
    }
}

/* Accessibility Enhancements */
.focus-visible:focus {
    outline: 2px solid var(--accent);
    outline-offset: 2px;
}

/* Reduced Motion */
@media (prefers-reduced-motion: reduce) {
    *,
    *::before,
    *::after {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }
}

/* High Contrast Mode */
@media (prefers-contrast: high) {
    .nav-button {
        border: 2px solid currentColor;
    }
    
    .card-hover {
        border: 2px solid var(--primary);
    }
}

/* Dark Mode Support */
@media (prefers-color-scheme: dark) {
    .bg-white {
        background-color: #1a1a1a;
        color: white;
    }
    
    .text-gray-600 {
        color: #d1d5db;
    }
    
    .text-gray-500 {
        color: #9ca3af;
    }
}

/* Loading Animation */
.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid var(--lightest-green);
    border-top: 4px solid var(--primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Scroll Indicator */
.scroll-indicator {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 4px;
    background: linear-gradient(90deg, var(--primary), var(--secondary), var(--accent));
    transform-origin: left;
    z-index: 9999;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: var(--lightest-green);
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(var(--primary), var(--secondary));
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(var(--secondary), var(--accent));
}

/* Print Styles */
@media print {
    .sidebar,
    .carousel-controls,
    .btn-primary,
    footer {
        display: none !important;
    }
    
    body {
        font-size: 12pt;
        line-height: 1.4;
    }
    
    h1, h2, h3 {
        page-break-after: avoid;
    }
}
