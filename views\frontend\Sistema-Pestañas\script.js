// AnimPlanetas Enhanced JavaScript Functionality

class AnimPlanetasApp {
    constructor() {
        this.currentSlide = 0;
        this.totalSlides = 3;
        this.isAutoPlaying = true;
        this.autoPlayInterval = null;
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.initializeCarousel();
        this.initializeAnimations();
        this.initializeScrollEffects();
        this.initializeAccessibility();
        this.startAutoPlay();
    }

    setupEventListeners() {
        // Sidebar functionality
        const menuToggle = document.getElementById('menu-toggle');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        
        if (menuToggle) {
            menuToggle.addEventListener('click', () => this.toggleSidebar());
        }
        
        if (sidebarOverlay) {
            sidebarOverlay.addEventListener('click', () => this.toggleSidebar());
        }

        // Dropdown functionality
        const navButtons = document.querySelectorAll('.nav-button');
        navButtons.forEach(button => {
            button.addEventListener('click', (e) => this.handleDropdown(e));
        });

        // Carousel controls
        const prevBtn = document.getElementById('prev-btn');
        const nextBtn = document.getElementById('next-btn');
        
        if (prevBtn) prevBtn.addEventListener('click', () => this.prevSlide());
        if (nextBtn) nextBtn.addEventListener('click', () => this.nextSlide());

        // Carousel indicators
        const indicators = document.querySelectorAll('.carousel-indicator');
        indicators.forEach((indicator, index) => {
            indicator.addEventListener('click', () => this.goToSlide(index));
        });

        // Pause auto-play on hover
        const carousel = document.getElementById('carousel');
        if (carousel) {
            carousel.addEventListener('mouseenter', () => this.pauseAutoPlay());
            carousel.addEventListener('mouseleave', () => this.resumeAutoPlay());
        }

        // Keyboard navigation
        document.addEventListener('keydown', (e) => this.handleKeyboard(e));

        // Window resize
        window.addEventListener('resize', () => this.handleResize());

        // Scroll events
        window.addEventListener('scroll', () => this.handleScroll());
    }

    toggleSidebar() {
        const sidebar = document.getElementById('sidebar');
        const sidebarOverlay = document.getElementById('sidebar-overlay');
        const mainContent = document.getElementById('main-content');

        if (sidebar && sidebarOverlay && mainContent) {
            sidebar.classList.toggle('-translate-x-full');
            sidebarOverlay.classList.toggle('hidden');
            
            // Handle responsive behavior
            if (window.innerWidth >= 1024) {
                if (!sidebar.classList.contains('-translate-x-full')) {
                    mainContent.classList.add('ml-80');
                } else {
                    mainContent.classList.remove('ml-80');
                }
            }
        }
    }

    handleDropdown(event) {
        const button = event.currentTarget;
        const dropdownId = button.getAttribute('data-dropdown');
        const dropdown = document.getElementById(`dropdown-${dropdownId}`);
        const chevron = button.querySelector('.fa-chevron-down');
        
        if (!dropdown || !chevron) return;

        // Close all other dropdowns
        document.querySelectorAll('.dropdown').forEach(d => {
            if (d !== dropdown && !d.classList.contains('hidden')) {
                d.classList.add('hidden');
                const otherChevron = d.parentElement.querySelector('.fa-chevron-down');
                if (otherChevron) {
                    otherChevron.style.transform = 'rotate(0deg)';
                }
            }
        });
        
        // Toggle current dropdown
        dropdown.classList.toggle('hidden');
        
        if (dropdown.classList.contains('hidden')) {
            chevron.style.transform = 'rotate(0deg)';
        } else {
            chevron.style.transform = 'rotate(180deg)';
        }
    }

    initializeCarousel() {
        this.updateCarousel();
    }

    updateCarousel() {
        const carousel = document.getElementById('carousel-inner');
        const indicators = document.querySelectorAll('.carousel-indicator');
        
        if (!carousel) return;

        const translateX = -this.currentSlide * 100;
        carousel.style.transform = `translateX(${translateX}%)`;
        
        // Update indicators
        indicators.forEach((indicator, index) => {
            if (index === this.currentSlide) {
                indicator.classList.remove('bg-white/50');
                indicator.classList.add('bg-white');
            } else {
                indicator.classList.remove('bg-white');
                indicator.classList.add('bg-white/50');
            }
        });
    }

    nextSlide() {
        this.currentSlide = (this.currentSlide + 1) % this.totalSlides;
        this.updateCarousel();
    }

    prevSlide() {
        this.currentSlide = (this.currentSlide - 1 + this.totalSlides) % this.totalSlides;
        this.updateCarousel();
    }

    goToSlide(index) {
        this.currentSlide = index;
        this.updateCarousel();
    }

    startAutoPlay() {
        if (this.isAutoPlaying) {
            this.autoPlayInterval = setInterval(() => {
                this.nextSlide();
            }, 5000);
        }
    }

    pauseAutoPlay() {
        if (this.autoPlayInterval) {
            clearInterval(this.autoPlayInterval);
            this.autoPlayInterval = null;
        }
    }

    resumeAutoPlay() {
        if (this.isAutoPlaying && !this.autoPlayInterval) {
            this.startAutoPlay();
        }
    }

    handleKeyboard(event) {
        // Sidebar toggle with Escape
        if (event.key === 'Escape') {
            const sidebar = document.getElementById('sidebar');
            if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                this.toggleSidebar();
            }
        }

        // Carousel navigation with arrow keys
        if (event.key === 'ArrowLeft') {
            this.prevSlide();
        } else if (event.key === 'ArrowRight') {
            this.nextSlide();
        }
    }

    handleResize() {
        // Close sidebar on mobile when resizing to desktop
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('main-content');
        
        if (window.innerWidth < 1024) {
            if (mainContent) {
                mainContent.classList.remove('ml-80');
            }
        }
    }

    handleScroll() {
        // Header shadow effect
        const header = document.querySelector('header');
        if (header) {
            if (window.scrollY > 100) {
                header.classList.add('shadow-xl');
            } else {
                header.classList.remove('shadow-xl');
            }
        }

        // Scroll progress indicator
        this.updateScrollProgress();
    }

    updateScrollProgress() {
        const scrollProgress = document.querySelector('.scroll-indicator');
        if (scrollProgress) {
            const scrollTop = window.pageYOffset;
            const docHeight = document.body.scrollHeight - window.innerHeight;
            const scrollPercent = (scrollTop / docHeight) * 100;
            scrollProgress.style.transform = `scaleX(${scrollPercent / 100})`;
        }
    }

    initializeAnimations() {
        // Intersection Observer for fade-in animations
        const observerOptions = {
            threshold: 0.1,
            rootMargin: '0px 0px -50px 0px'
        };

        const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    entry.target.classList.add('animate-fade-in');
                    
                    // Animate statistics counters
                    if (entry.target.classList.contains('stat-number')) {
                        this.animateCounter(entry.target);
                    }
                }
            });
        }, observerOptions);

        // Observe sections and statistics
        document.querySelectorAll('section, .stat-number').forEach(element => {
            observer.observe(element);
        });
    }

    animateCounter(element) {
        const target = parseInt(element.textContent.replace(/[^\d]/g, ''));
        const duration = 2000;
        const step = target / (duration / 16);
        let current = 0;

        const timer = setInterval(() => {
            current += step;
            if (current >= target) {
                current = target;
                clearInterval(timer);
            }
            
            const suffix = element.textContent.replace(/[\d,]/g, '');
            element.textContent = Math.floor(current).toLocaleString() + suffix;
        }, 16);
    }

    initializeScrollEffects() {
        // Create scroll progress indicator
        const scrollIndicator = document.createElement('div');
        scrollIndicator.className = 'scroll-indicator';
        document.body.appendChild(scrollIndicator);

        // Smooth scrolling for anchor links
        document.querySelectorAll('a[href^="#"]').forEach(anchor => {
            anchor.addEventListener('click', (e) => {
                e.preventDefault();
                const target = document.querySelector(anchor.getAttribute('href'));
                if (target) {
                    target.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start'
                    });
                }
            });
        });
    }

    initializeAccessibility() {
        // Add ARIA labels and roles
        const sidebar = document.getElementById('sidebar');
        if (sidebar) {
            sidebar.setAttribute('role', 'navigation');
            sidebar.setAttribute('aria-label', 'Main navigation');
        }

        // Add focus management for dropdowns
        const navButtons = document.querySelectorAll('.nav-button');
        navButtons.forEach(button => {
            button.setAttribute('aria-expanded', 'false');
            button.addEventListener('click', () => {
                const isExpanded = button.getAttribute('aria-expanded') === 'true';
                button.setAttribute('aria-expanded', !isExpanded);
            });
        });

        // Add carousel accessibility
        const carousel = document.getElementById('carousel');
        if (carousel) {
            carousel.setAttribute('role', 'region');
            carousel.setAttribute('aria-label', 'Featured content carousel');
        }
    }
}

// Initialize the application when DOM is loaded
document.addEventListener('DOMContentLoaded', () => {
    new AnimPlanetasApp();
});

// Export for potential module usage
if (typeof module !== 'undefined' && module.exports) {
    module.exports = AnimPlanetasApp;
}
