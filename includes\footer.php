    </div> <!-- End main-content -->

    <!-- Footer -->
    <footer class="bg-primary text-white py-12">
        <div class="container mx-auto px-4 desktop:max-w-7xl qhd:max-w-8xl uhd:max-w-full">
            <div class="grid grid-cols-1 tablet:grid-cols-2 laptop:grid-cols-4 gap-8">
                <!-- Logo and Description -->
                <div class="laptop:col-span-2">
                    <div class="flex items-center space-x-3 mb-4">
                        <div class="w-12 h-12 bg-white rounded-full flex items-center justify-center">
                            <i class="fas fa-paw text-primary text-xl"></i>
                        </div>
                        <h3 class="text-2xl font-bold"><?php echo SITE_NAME; ?></h3>
                    </div>
                    <p class="text-lighter-green mb-4 max-w-md">
                        <?php echo SITE_DESCRIPTION; ?>. Educación inclusiva y accesible para todos.
                    </p>
                    <div class="flex space-x-4">
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-facebook text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-twitter text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-instagram text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-youtube text-2xl"></i>
                        </a>
                        <a href="#" class="text-lighter-green hover:text-white transition-colors">
                            <i class="fab fa-tiktok text-2xl"></i>
                        </a>
                    </div>
                </div>

                <!-- Quick Links -->
                <div>
                    <h4 class="text-lg font-bold mb-4">Enlaces Rápidos</h4>
                    <ul class="space-y-2">
                        <?php foreach (array_slice(NAVIGATION_ITEMS, 0, 5) as $key => $item): ?>
                        <li>
                            <a href="<?php echo $item['url']; ?>" class="text-lighter-green hover:text-white transition-colors">
                                <?php echo $item['title']; ?>
                            </a>
                        </li>
                        <?php endforeach; ?>
                        <li>
                            <a href="blog.php" class="text-lighter-green hover:text-white transition-colors">Blog</a>
                        </li>
                        <li>
                            <a href="contact.php" class="text-lighter-green hover:text-white transition-colors">Contacto</a>
                        </li>
                    </ul>
                </div>

                <!-- Contact Info -->
                <div>
                    <h4 class="text-lg font-bold mb-4">Contacto</h4>
                    <ul class="space-y-2">
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-envelope text-lighter-green"></i>
                            <span class="text-lighter-green"><EMAIL></span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-phone text-lighter-green"></i>
                            <span class="text-lighter-green">+52 (*************</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-map-marker-alt text-lighter-green"></i>
                            <span class="text-lighter-green">Ciudad de México, México</span>
                        </li>
                        <li class="flex items-center space-x-2">
                            <i class="fas fa-clock text-lighter-green"></i>
                            <span class="text-lighter-green">Lun - Vie: 9:00 - 18:00</span>
                        </li>
                    </ul>
                    
                    <!-- Newsletter -->
                    <div class="mt-6">
                        <h5 class="font-semibold mb-2">Suscríbete a nuestro boletín</h5>
                        <form class="flex" onsubmit="subscribeNewsletter(event)">
                            <input type="email" placeholder="Tu email" required
                                   class="flex-1 px-3 py-2 text-gray-800 rounded-l-lg focus:outline-none">
                            <button type="submit" class="bg-secondary hover:bg-accent px-4 py-2 rounded-r-lg transition-colors">
                                <i class="fas fa-paper-plane"></i>
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Bottom Footer -->
            <div class="border-t border-accent/30 mt-8 pt-8">
                <div class="flex flex-col tablet:flex-row justify-between items-center">
                    <p class="text-lighter-green text-center tablet:text-left mb-4 tablet:mb-0">
                        © <?php echo date('Y'); ?> <?php echo SITE_NAME; ?>. Todos los derechos reservados.
                    </p>
                    <div class="flex space-x-6">
                        <a href="privacy.php" class="text-lighter-green hover:text-white transition-colors">
                            Política de Privacidad
                        </a>
                        <a href="terms.php" class="text-lighter-green hover:text-white transition-colors">
                            Términos de Servicio
                        </a>
                        <a href="cookies.php" class="text-lighter-green hover:text-white transition-colors">
                            Política de Cookies
                        </a>
                    </div>
                </div>
                
                <!-- Version and Credits -->
                <div class="text-center mt-4 pt-4 border-t border-accent/20">
                    <p class="text-sm text-lighter-green">
                        Versión <?php echo SITE_VERSION; ?> | 
                        Desarrollado con ❤️ para la comunidad de lenguaje de señas
                    </p>
                </div>
            </div>
        </div>
    </footer>

    <!-- Overlay for sidebar -->
    <div id="sidebar-overlay" class="fixed inset-0 bg-black bg-opacity-50 z-40 hidden transition-opacity duration-300"></div>

    <!-- Cart Sidebar -->
    <div id="cart-sidebar" class="fixed right-0 top-0 h-full w-96 bg-white transform translate-x-full transition-transform duration-300 ease-in-out z-50 shadow-2xl">
        <div class="p-6 border-b border-lighter-green">
            <div class="flex items-center justify-between">
                <h3 class="text-xl font-bold text-primary">Carrito de Compras</h3>
                <button onclick="toggleCart()" class="p-2 hover:bg-lightest-green rounded-lg">
                    <i class="fas fa-times text-primary"></i>
                </button>
            </div>
        </div>
        
        <div id="cart-content" class="p-6 overflow-y-auto max-h-[calc(100vh-200px)]">
            <!-- Cart items will be loaded here -->
        </div>
        
        <div class="absolute bottom-0 left-0 right-0 p-6 border-t border-lighter-green bg-white">
            <div class="flex justify-between items-center mb-4">
                <span class="font-bold text-primary">Total:</span>
                <span id="cart-total" class="font-bold text-xl text-primary">$0.00</span>
            </div>
            <button class="w-full bg-primary hover:bg-secondary text-white py-3 rounded-lg transition-colors">
                Proceder al Checkout
            </button>
        </div>
    </div>

    <!-- JavaScript -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log('AnimPlanetas: DOM loaded, initializing...');
            
            // Sidebar functionality
            const sidebar = document.getElementById('sidebar');
            const sidebarOverlay = document.getElementById('sidebar-overlay');
            const menuToggle = document.getElementById('menu-toggle');
            const mainContent = document.getElementById('main-content');
            
            function toggleSidebar() {
                if (sidebar && sidebarOverlay && mainContent) {
                    const isOpen = !sidebar.classList.contains('-translate-x-full');
                    
                    if (isOpen) {
                        sidebar.classList.add('-translate-x-full');
                        sidebarOverlay.classList.add('hidden');
                        if (window.innerWidth >= 1024) {
                            mainContent.classList.remove('laptop:ml-80');
                        }
                    } else {
                        sidebar.classList.remove('-translate-x-full');
                        sidebarOverlay.classList.remove('hidden');
                        if (window.innerWidth >= 1024) {
                            mainContent.classList.add('laptop:ml-80');
                        }
                    }
                }
            }

            if (menuToggle) {
                menuToggle.addEventListener('click', toggleSidebar);
            }
            if (sidebarOverlay) {
                sidebarOverlay.addEventListener('click', toggleSidebar);
            }

            // Dropdown functionality
            const navButtons = document.querySelectorAll('.nav-button');
            
            navButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const dropdownId = this.getAttribute('data-dropdown');
                    const dropdown = document.getElementById(`dropdown-${dropdownId}`);
                    const chevron = this.querySelector('.fa-chevron-down');
                    
                    if (dropdown && chevron) {
                        // Close all other dropdowns
                        document.querySelectorAll('.dropdown').forEach(d => {
                            if (d !== dropdown && !d.classList.contains('hidden')) {
                                d.classList.add('hidden');
                                const otherChevron = d.parentElement.querySelector('.fa-chevron-down');
                                if (otherChevron) {
                                    otherChevron.style.transform = 'rotate(0deg)';
                                }
                            }
                        });
                        
                        // Toggle current dropdown
                        dropdown.classList.toggle('hidden');
                        
                        if (dropdown.classList.contains('hidden')) {
                            chevron.style.transform = 'rotate(0deg)';
                        } else {
                            chevron.style.transform = 'rotate(180deg)';
                        }
                    }
                });
            });

            // Scroll effects
            window.addEventListener('scroll', function() {
                const header = document.querySelector('header');
                if (header) {
                    if (window.scrollY > 100) {
                        header.classList.add('shadow-xl');
                    } else {
                        header.classList.remove('shadow-xl');
                    }
                }
            });

            // Keyboard navigation
            document.addEventListener('keydown', function(e) {
                if (e.key === 'Escape') {
                    const sidebar = document.getElementById('sidebar');
                    if (sidebar && !sidebar.classList.contains('-translate-x-full')) {
                        toggleSidebar();
                    }
                    
                    const cartSidebar = document.getElementById('cart-sidebar');
                    if (cartSidebar && !cartSidebar.classList.contains('translate-x-full')) {
                        toggleCart();
                    }
                    
                    const profileDropdown = document.getElementById('profile-dropdown');
                    if (profileDropdown && !profileDropdown.classList.contains('hidden')) {
                        toggleProfile();
                    }
                }
            });

            console.log('AnimPlanetas: All functionality initialized successfully!');
        });

        // Cart functionality
        function toggleCart() {
            const cartSidebar = document.getElementById('cart-sidebar');
            if (cartSidebar) {
                cartSidebar.classList.toggle('translate-x-full');
                loadCartContent();
            }
        }

        function loadCartContent() {
            // Load cart content via AJAX
            fetch('ajax/cart.php')
                .then(response => response.text())
                .then(html => {
                    document.getElementById('cart-content').innerHTML = html;
                    updateCartTotal();
                })
                .catch(error => console.error('Error loading cart:', error));
        }

        function updateCartTotal() {
            fetch('ajax/cart_total.php')
                .then(response => response.json())
                .then(data => {
                    document.getElementById('cart-total').textContent = '$' + data.total.toFixed(2);
                })
                .catch(error => console.error('Error updating cart total:', error));
        }

        // Profile dropdown
        function toggleProfile() {
            const dropdown = document.getElementById('profile-dropdown');
            if (dropdown) {
                dropdown.classList.toggle('hidden');
            }
        }

        // Newsletter subscription
        function subscribeNewsletter(event) {
            event.preventDefault();
            const email = event.target.querySelector('input[type="email"]').value;
            
            fetch('ajax/newsletter.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ email: email })
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('¡Gracias por suscribirte!');
                    event.target.reset();
                } else {
                    alert('Error al suscribirse. Inténtalo de nuevo.');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('Error al suscribirse. Inténtalo de nuevo.');
            });
        }

        // Close dropdowns when clicking outside
        document.addEventListener('click', function(event) {
            const profileDropdown = document.getElementById('profile-dropdown');
            const profileButton = event.target.closest('[onclick="toggleProfile()"]');
            
            if (profileDropdown && !profileButton && !profileDropdown.contains(event.target)) {
                profileDropdown.classList.add('hidden');
            }
        });
    </script>
</body>
</html>
