<?php
/**
 * Hero Component - Sección principal con carousel
 */

// Obtener elementos del carousel
$carouselItems = getCarouselItems();
if (empty($carouselItems)) {
    // Datos de ejemplo si no hay en la base de datos
    $carouselItems = [
        [
            'id' => 1,
            'title' => 'AnimaleSeñas',
            'description' => 'Aprende las señas de tus animales favoritos',
            'icon' => 'fas fa-dog',
            'color' => 'primary',
            'image' => 'hero-animals.jpg'
        ],
        [
            'id' => 2,
            'title' => 'PlanetaSeñas',
            'description' => 'Explora el mundo de las señas planetarias',
            'icon' => 'fas fa-globe',
            'color' => 'secondary',
            'image' => 'hero-planet.jpg'
        ],
        [
            'id' => 3,
            'title' => 'JuegoSeñas',
            'description' => 'Diviértete mientras aprendes',
            'icon' => 'fas fa-gamepad',
            'color' => 'accent',
            'image' => 'hero-games.jpg'
        ]
    ];
}
?>

<!-- Hero Section with Carousel -->
<section class="bg-gradient-to-r from-primary to-secondary py-20 relative overflow-hidden">
    <!-- Background Pattern -->
    <div class="absolute inset-0 opacity-10">
        <div class="absolute inset-0" style="background-image: url('data:image/svg+xml,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"white\" opacity=\"0.3\"/></svg>'); background-size: 50px 50px;"></div>
    </div>
    
    <div class="container mx-auto px-4 relative z-10 desktop:max-w-7xl qhd:max-w-8xl uhd:max-w-full">
        <div class="text-center text-white mb-12">
            <h2 class="text-3xl compact:text-4xl tablet:text-5xl laptop:text-6xl font-bold mb-4 animate-fade-in">
                Aprende Lenguaje de Señas
            </h2>
            <p class="text-lg tablet:text-xl text-lighter-green max-w-3xl mx-auto animate-fade-in">
                Conecta con el mundo animal a través del lenguaje de señas. 
                Una experiencia educativa única e inclusiva para toda la familia.
            </p>
        </div>
        
        <!-- Carousel -->
        <div class="relative max-w-6xl mx-auto">
            <div id="hero-carousel" class="overflow-hidden rounded-2xl shadow-2xl">
                <div class="flex transition-transform duration-500 ease-in-out" id="hero-carousel-inner">
                    <?php foreach ($carouselItems as $index => $item): ?>
                    <div class="w-full flex-shrink-0">
                        <div class="bg-white p-8 tablet:p-12 text-center min-h-[300px] flex flex-col justify-center">
                            <div class="mb-6">
                                <i class="<?php echo $item['icon']; ?> text-6xl tablet:text-8xl text-<?php echo $item['color']; ?> mb-4"></i>
                            </div>
                            <h3 class="text-2xl tablet:text-3xl font-bold text-primary mb-4">
                                <?php echo htmlspecialchars($item['title']); ?>
                            </h3>
                            <p class="text-gray-600 text-lg max-w-md mx-auto mb-6">
                                <?php echo htmlspecialchars($item['description']); ?>
                            </p>
                            <div class="flex flex-col compact:flex-row gap-4 justify-center">
                                <button class="bg-primary hover:bg-secondary text-white px-6 py-3 rounded-lg font-semibold transition-all transform hover:scale-105">
                                    Explorar Ahora
                                </button>
                                <button class="border-2 border-primary text-primary hover:bg-primary hover:text-white px-6 py-3 rounded-lg font-semibold transition-all">
                                    Ver Video
                                </button>
                            </div>
                        </div>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
            
            <!-- Carousel Controls -->
            <button id="hero-prev-btn" class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all backdrop-blur-sm">
                <i class="fas fa-chevron-left"></i>
            </button>
            <button id="hero-next-btn" class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-white/20 hover:bg-white/30 text-white p-3 rounded-full transition-all backdrop-blur-sm">
                <i class="fas fa-chevron-right"></i>
            </button>
            
            <!-- Carousel Indicators -->
            <div class="flex justify-center mt-6 space-x-2">
                <?php foreach ($carouselItems as $index => $item): ?>
                <button class="hero-carousel-indicator w-3 h-3 bg-white bg-opacity-50 rounded-full transition-all hover:bg-opacity-75" 
                        data-slide="<?php echo $index; ?>"></button>
                <?php endforeach; ?>
            </div>
        </div>
        
        <!-- Stats Preview -->
        <div class="grid grid-cols-2 tablet:grid-cols-4 gap-4 mt-16 max-w-4xl mx-auto">
            <?php 
            $stats = getStatistics();
            $statItems = [
                ['icon' => 'fas fa-users', 'value' => number_format($stats['users']), 'label' => 'Estudiantes'],
                ['icon' => 'fas fa-video', 'value' => number_format($stats['videos']), 'label' => 'Videos'],
                ['icon' => 'fas fa-trophy', 'value' => '95%', 'label' => 'Éxito'],
                ['icon' => 'fas fa-star', 'value' => number_format($stats['rating'], 1), 'label' => 'Rating']
            ];
            ?>
            <?php foreach ($statItems as $stat): ?>
            <div class="text-center text-white">
                <i class="<?php echo $stat['icon']; ?> text-2xl tablet:text-3xl mb-2 text-lighter-green"></i>
                <div class="text-xl tablet:text-2xl font-bold"><?php echo $stat['value']; ?></div>
                <div class="text-sm tablet:text-base text-lighter-green"><?php echo $stat['label']; ?></div>
            </div>
            <?php endforeach; ?>
        </div>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Hero Carousel functionality
    const heroCarousel = document.getElementById('hero-carousel-inner');
    const heroPrevBtn = document.getElementById('hero-prev-btn');
    const heroNextBtn = document.getElementById('hero-next-btn');
    const heroIndicators = document.querySelectorAll('.hero-carousel-indicator');
    
    let heroCurrentSlide = 0;
    const heroTotalSlides = <?php echo count($carouselItems); ?>;

    function updateHeroCarousel() {
        if (heroCarousel) {
            const translateX = -heroCurrentSlide * 100;
            heroCarousel.style.transform = `translateX(${translateX}%)`;
            
            // Update indicators
            heroIndicators.forEach((indicator, index) => {
                if (index === heroCurrentSlide) {
                    indicator.classList.remove('bg-opacity-50');
                    indicator.classList.add('bg-opacity-100');
                } else {
                    indicator.classList.remove('bg-opacity-100');
                    indicator.classList.add('bg-opacity-50');
                }
            });
        }
    }

    function heroNextSlide() {
        heroCurrentSlide = (heroCurrentSlide + 1) % heroTotalSlides;
        updateHeroCarousel();
    }

    function heroPrevSlide() {
        heroCurrentSlide = (heroCurrentSlide - 1 + heroTotalSlides) % heroTotalSlides;
        updateHeroCarousel();
    }

    if (heroNextBtn) {
        heroNextBtn.addEventListener('click', heroNextSlide);
    }
    if (heroPrevBtn) {
        heroPrevBtn.addEventListener('click', heroPrevSlide);
    }

    // Indicator click functionality
    heroIndicators.forEach((indicator, index) => {
        indicator.addEventListener('click', () => {
            heroCurrentSlide = index;
            updateHeroCarousel();
        });
    });

    // Auto-play carousel
    setInterval(heroNextSlide, 6000);

    // Initialize carousel
    updateHeroCarousel();
    
    // Pause auto-play on hover
    const heroCarouselContainer = document.getElementById('hero-carousel');
    if (heroCarouselContainer) {
        let autoPlayInterval = setInterval(heroNextSlide, 6000);
        
        heroCarouselContainer.addEventListener('mouseenter', () => {
            clearInterval(autoPlayInterval);
        });
        
        heroCarouselContainer.addEventListener('mouseleave', () => {
            autoPlayInterval = setInterval(heroNextSlide, 6000);
        });
    }
});
</script>
